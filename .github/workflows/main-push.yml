name: Main Push
on:
  push:
    branches:
      - main
jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    environment: Development
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install pnpm
        uses: pnpm/action-setup@v4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          cache: pnpm
          node-version-file: package.json
      - name: Install Dependencies
        run: pnpm --filter @rackup/api install
      - name: Deploy
        uses: cloudflare/wrangler-action@v3
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          DETECT_UNUSED_QUERY_ARGUMENTS: ${{ vars.DETECT_UNUSED_QUERY_ARGUMENTS }}
        with:
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          environment: development
          secrets: |
            DATABASE_URL
          vars: |
            DETECT_UNUSED_QUERY_ARGUMENTS
          workingDirectory: packages/api
      - name: Migrate Database
        env:
          DATABASE_DIRECT_URL: ${{ secrets.DATABASE_DIRECT_URL }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: pnpm --filter @rackup/api exec prisma migrate deploy
      - name: Seed Database
        env:
          DATABASE_DIRECT_URL: ${{ secrets.DATABASE_DIRECT_URL }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
        run: pnpm --filter @rackup/api exec prisma db seed
