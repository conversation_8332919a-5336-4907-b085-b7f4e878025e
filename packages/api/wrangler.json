{"$schema": "node_modules/wrangler/config-schema.json", "compatibility_date": "2025-07-09", "compatibility_flags": ["nodejs_compat"], "dev": {"port": 9000}, "env": {"local": {"vars": {"DATABASE_URL": "postgres://postgres:postgres@127.0.0.1:54322/postgres", "DETECT_UNUSED_QUERY_ARGUMENTS": true}}, "development": {"name": "rackup-api-dev", "preview_urls": false, "routes": [{"pattern": "api.dev.rackup.fitness", "custom_domain": true}], "workers_dev": false}}, "main": "src/index.ts", "name": "rackup-api", "observability": {"enabled": true}, "send_metrics": false}