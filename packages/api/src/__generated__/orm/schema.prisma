model Exercise {
  id        Int      @id @default(autoincrement())
  name      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

datasource database {
  directUrl = env("DATABASE_DIRECT_URL")
  provider  = "postgresql"
  url       = env("DATABASE_URL") // Overridden by 'createClient' when executing within worker
}

generator graphql_schema {
  clientOutput      = "./orm"
  generateDatamodel = true
  output            = "../__generated__/graphql-schema.ts"
  provider          = "prisma-pothos-types"
}

generator orm {
  binaryTargets   = ["native", "debian-openssl-3.0.x"] // 'debian' to support execution on GitHub Actions
  output          = "../__generated__/orm"
  previewFeatures = ["driverAdapters"]
  provider        = "prisma-client-js"
}
