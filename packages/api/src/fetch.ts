import { createYoga } from "graphql-yoga";

import { createBuilder } from "./createBuilder";
import { createClient } from "./createClient";
import { createSchema } from "./graphql";
import { renderGraphiQL } from "./utils";

const fetch: ExportedHandlerFetchHandler<Env> = (request, env) => {
  const client = createClient(env.DATABASE_URL);
  const builder = createBuilder(
    client,
    env.DETECT_UNUSED_QUERY_ARGUMENTS ? "warn" : null,
  );
  const schema = createSchema(builder, client);

  const yoga = createYoga({
    renderGraphiQL,
    schema,
  });

  return yoga.fetch(request, env);
};

export { fetch };
