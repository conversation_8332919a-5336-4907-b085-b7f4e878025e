import SchemaBuilder from "@pothos/core";
import PothosPluginPrisma from "@pothos/plugin-prisma";
import PothosPluginWithInput from "@pothos/plugin-with-input";
import { DateTimeResolver } from "graphql-scalars";

import type PrismaTypes from "./__generated__/graphql-schema";
import { getDatamodel } from "./__generated__/graphql-schema";
import { PrismaClient } from "./__generated__/orm/edge";

const createBuilder = (
  client: PrismaClient,
  onUnusedQuery: "warn" | "error" | null,
) => {
  const builder = new SchemaBuilder<{
    PrismaTypes: PrismaTypes;
    Scalars: {
      DateTime: { Input: Date; Output: Date };
    };
  }>({
    plugins: [PothosPluginPrisma, PothosPluginWithInput],
    prisma: {
      client,
      dmmf: getDatamodel(),
      onUnusedQuery,
    },
  });

  builder.addScalarType("DateTime", DateTimeResolver);

  return builder;
};

export { createBuilder };
