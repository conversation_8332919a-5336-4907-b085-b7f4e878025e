import { PrismaClient } from "../../types";

const NAMES = [
  "21's",
  "Face Pull",
  "Lat Pulldown",
  "Seated Row",
  "Tricep Kickbacks",
  "Weighted Dips",
];

const exercise = async (client: PrismaClient) => {
  await Promise.all(
    NAMES.map((name) =>
      client.exercise.upsert({
        create: { name },
        update: {},
        where: { name },
      }),
    ),
  );
};

export { exercise };
