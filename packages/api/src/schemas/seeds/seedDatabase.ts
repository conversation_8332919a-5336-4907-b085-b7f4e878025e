import { createClient } from "../../createClient";
import { exercise } from "./exercise";

const seedDatabase = () => {
  const client = createClient(process.env.DATABASE_URL);

  Promise.all([exercise(client)])
    .then(async () => {
      await client.$disconnect();
    })
    .catch(async (error) => {
      console.error(error);

      await client.$disconnect();

      process.exit(1);
    });
};

seedDatabase();
