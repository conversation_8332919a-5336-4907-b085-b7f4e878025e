import { RuleConfigSeverity, type UserConfig } from "@commitlint/types";

const config: UserConfig = {
  extends: ["@commitlint/config-conventional"],
  parserPreset: {
    parserOpts: {
      issuePrefixes: ["RACKUP-"],
    },
  },
  rules: {
    "references-empty": [RuleConfigSeverity.Error, "never"],
    "scope-enum": [RuleConfigSeverity.Error, "always", ["api"]],
    "subject-max-length": [RuleConfigSeverity.Error, "always", 50],
  },
};

export default config;
